# Tomcat 构建脚本下载优化总结

## 优化目标
优化 `build-tomcat.sh` 脚本，如果存在 `apache-tomcat-9.0.100.tar.gz.sha512` 文件就跳过下载步骤，用已存在的文件进行校验。

## 修改的文件
- `docker/scripts/lib/download.sh`

## 具体优化内容

### 原始逻辑
在 `download_from_url` 函数中：
1. 每次都尝试从远程下载 SHA512 校验和文件
2. 然后检查本地文件是否有效

### 优化后的逻辑
在 `download_from_url` 函数中：
1. **首先检查本地是否已存在 SHA512 文件**
2. 如果本地 SHA512 文件存在且格式有效，直接使用本地文件
3. 只有在本地没有有效 SHA512 文件时，才尝试从远程下载
4. 然后检查本地文件是否有效

### 代码变更详情

在 `download_from_url` 函数的第137-167行，添加了以下逻辑：

```bash
# 获取SHA512校验和（如果需要验证）
local expected_sha512=""
local sha512_file="${output_file}.sha512"
if [[ "$verify_checksum" == "true" ]]; then
    # 首先检查本地是否已存在SHA512文件
    if [[ -f "$sha512_file" ]] && [[ -s "$sha512_file" ]]; then
        log_info "发现本地SHA512文件，使用已存在的文件: $sha512_file" >&2
        # 提取SHA512值，移除所有空格和换行符
        expected_sha512=$(tr -d '[:space:]' < "$sha512_file" | grep -o '^[a-fA-F0-9]\{128\}')
        
        if [[ "$expected_sha512" =~ ^[a-fA-F0-9]{128}$ ]]; then
            log_info "✓ 成功从本地文件获取SHA512校验和" >&2
        else
            log_warn "本地SHA512文件格式无效，将重新下载" >&2
            expected_sha512=""
        fi
    fi
    
    # 如果本地没有有效的SHA512文件，则尝试下载
    if [[ -z "$expected_sha512" ]]; then
        local sha512_url="${download_url}.sha512"
        log_info "尝试下载SHA512校验和文件: $sha512_url" >&2
        expected_sha512=$(download_sha512_file "$sha512_url" "$sha512_file")
        if [[ $? -eq 0 ]]; then
            log_info "成功下载SHA512校验和" >&2
        else
            log_warn "无法获取SHA512校验和，将跳过文件验证" >&2
            verify_checksum="false"
        fi
    fi
fi
```

## 优化效果

### 性能提升
1. **减少网络请求**: 如果本地已有有效的 SHA512 文件，跳过下载步骤
2. **加快构建速度**: 避免不必要的网络延迟
3. **提高可靠性**: 减少对网络连接的依赖

### 功能保持
1. **向后兼容**: 如果本地没有 SHA512 文件，仍然会尝试下载
2. **校验完整性**: 保持原有的文件校验逻辑
3. **错误处理**: 如果本地 SHA512 文件格式无效，会重新下载

### 日志输出
优化后的脚本会输出更详细的日志信息：
- `发现本地SHA512文件，使用已存在的文件: xxx.sha512`
- `✓ 成功从本地文件获取SHA512校验和`
- `本地SHA512文件格式无效，将重新下载`

## 测试验证
通过测试脚本验证了优化功能：
1. 创建模拟的 SHA512 文件
2. 调用 `download_from_url` 函数
3. 确认脚本正确识别并使用本地 SHA512 文件
4. 验证日志输出符合预期

## 使用场景
这个优化特别适用于以下场景：
1. **重复构建**: 在同一环境中多次构建相同版本的 Tomcat
2. **离线环境**: 在网络受限的环境中进行构建
3. **CI/CD 流水线**: 在持续集成环境中缓存校验和文件以提高效率

## 总结
此优化在保持原有功能完整性的前提下，通过智能检测本地 SHA512 文件来减少不必要的网络下载，提高了构建脚本的效率和可靠性。
