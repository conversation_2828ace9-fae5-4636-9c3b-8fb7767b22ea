# 使用我们自己构建的OS基础镜像
ARG BASE_IMAGE
FROM $BASE_IMAGE

# 设置JDK环境变量
ENV JAVA_HOME=/usr/local/jdk${JDK_FULL_VERSION}
ENV PATH=$JAVA_HOME/bin:$PATH

# 复制对应架构的JDK安装包
COPY shared/jdk/${JDK_VERSION}/${ARCH_DIR}/jdk${JDK_FULL_VERSION}.tar.gz /tmp/

# 安装JDK
RUN mkdir -p $JAVA_HOME && \
    tar -zxf /tmp/jdk${JDK_FULL_VERSION}.tar.gz -C /tmp && \
    mv /tmp/jdk${JDK_FULL_VERSION}/* $JAVA_HOME && \
    rm -rf /tmp/jdk${JDK_FULL_VERSION}.tar.gz /tmp/jdk${JDK_FULL_VERSION} && \
    update-alternatives --install /usr/bin/java java $JAVA_HOME/bin/java 100 && \
    update-alternatives --install /usr/bin/javac javac $JAVA_HOME/bin/javac 100

# 替换cacerts
COPY shared/certs/cacerts $JAVA_HOME/jre/lib/security/cacerts

# 设置工作目录
WORKDIR /app

# 添加标签信息
LABEL maintainer="btit-team" \
      version="${JDK_VERSION}" \
      description="JDK ${JDK_VERSION} (1.8.0_172) base image for ${ARCH} architecture" \
      base.image="ubuntu:${UBUNTU_VERSION}" \
      build.date="$(date -u +'%Y-%m-%dT%H:%M:%SZ')" \
      vendor="BTIT" \
      architecture="${ARCH}" \
      java.version="${JDK_VERSION}"

# 创建应用目录
RUN mkdir -p /app \
    && mkdir -p /app/logs \
    && mkdir -p /app/temp

