# JDK构建脚本架构支持说明

## 概述

本次修改为JDK构建脚本和Dockerfile模板添加了完整的ARM64和AMD64架构支持，使得构建系统能够根据目标架构自动选择正确的JDK安装包。

## 修改内容

### 1. 构建脚本修改 (`docker/scripts/build/build-jdk.sh`)

#### 新增功能
- **架构转换函数**: `docker_arch_to_dir_arch()`
  - 将Docker架构名称转换为目录架构名称
  - `amd64` -> `x86_64`
  - `arm64` -> `aarch64`

#### 主要修改点
1. **移除架构限制**: 删除了阻止跨架构构建的检查逻辑
2. **智能包检查**: 改进JDK安装包存在性检查，支持多架构验证
3. **架构相关路径**: 根据目标架构选择正确的JDK安装包路径
4. **变量替换增强**: 添加了 `ARCH_DIR` 和 `JDK_FULL_VERSION` 变量

### 2. Dockerfile模板修改 (`docker/infrastructure/jdk/Dockerfile.template`)

#### 主要变更
- **动态路径**: 使用 `${ARCH_DIR}` 变量指定架构相关的JDK安装包路径
- **版本变量**: 使用 `${JDK_FULL_VERSION}` 变量支持动态版本号
- **环境变量**: JAVA_HOME路径使用动态版本号

## 目录结构

```
docker/infrastructure/jdk/shared/jdk/
├── 8/
│   ├── aarch64/          # ARM64架构的JDK安装包
│   │   └── jdk1.8.0_172.tar.gz
│   └── x86_64/           # AMD64架构的JDK安装包
│       └── jdk1.8.0_172.tar.gz
└── README.md
```

## 配置文件

`docker/scripts/build/versions/jdk_versions.json` 中的架构配置：

```json
{
  "versions": {
    "8": {
      "jdk_version": "1.8.0_172",
      "build_number": "01",
      "variants": ["jammy"],
      "supported_architectures": ["amd64", "arm64"]
    }
  }
}
```

## 使用方法

### 构建特定架构的镜像

```bash
# 构建AMD64架构的JDK 8镜像
./docker/scripts/build/build-jdk.sh 8

# 构建ARM64架构的JDK 8镜像（如果当前系统支持）
./docker/scripts/build/build-jdk.sh 8
```

### 跨架构构建

现在支持在任何架构的系统上构建目标架构的镜像，只要：
1. 目标架构的JDK安装包存在于正确的目录中
2. Docker支持目标架构的构建

## 验证方法

### 1. 检查安装包结构
确保JDK安装包按照以下结构放置：
```
docker/infrastructure/jdk/shared/jdk/8/
├── aarch64/jdk1.8.0_172.tar.gz
└── x86_64/jdk1.8.0_172.tar.gz
```

### 2. 运行测试脚本
```bash
./docker/scripts/build/test-arch-support.sh
```

### 3. 验证构建过程
构建时会显示架构信息：
```
开始构建 JDK 8 (jammy) for amd64
目标架构: amd64 (目录架构: x86_64)
```

## 错误处理

### 常见错误及解决方案

1. **JDK安装包不存在**
   ```
   错误: JDK安装包不存在: .../shared/jdk/8/x86_64/jdk1.8.0_172.tar.gz
   ```
   **解决**: 确保JDK安装包放置在正确的架构目录中

2. **架构不支持**
   ```
   警告: 版本配置文件中未找到JDK 8 的配置
   ```
   **解决**: 检查 `jdk_versions.json` 中的架构配置

## 兼容性

- **向后兼容**: 现有的构建流程不受影响
- **多架构支持**: 同时支持AMD64和ARM64架构
- **配置驱动**: 通过配置文件控制支持的架构列表

## 注意事项

1. 确保JDK安装包文件名与配置文件中的版本号一致
2. 不同架构的JDK安装包必须放置在对应的架构目录中
3. 构建时会自动检查所有必需的安装包是否存在
4. 支持的架构列表在 `jdk_versions.json` 中配置
