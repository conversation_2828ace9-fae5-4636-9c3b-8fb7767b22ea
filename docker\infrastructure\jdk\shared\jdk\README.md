# JDK安装包目录

此目录用于存放预下载的JDK安装包，用于构建Docker镜像时离线安装JDK。支持多架构（AMD64和ARM64）的JDK安装包。

## 目录结构

```
jdk/
├── 8/                           # JDK 8版本目录
│   ├── aarch64/                 # ARM64架构目录
│   │   └── jdk1.8.0_172.tar.gz  # ARM64版本的JDK 8 u172安装包
│   ├── x86_64/                  # AMD64架构目录
│   │   └── jdk1.8.0_172.tar.gz  # AMD64版本的JDK 8 u172安装包
│   └── README.md                # 版本说明文件（可选）
└── README.md                    # 本说明文件
```

## 架构支持

构建系统支持以下架构：

| 架构类型 | Docker架构名 | 目录名称 | 说明 |
|---------|-------------|---------|------|
| AMD64 | `amd64` | `x86_64` | Intel/AMD 64位处理器 |
| ARM64 | `arm64` | `aarch64` | ARM 64位处理器 |

## 使用说明

1. **下载JDK安装包**：为每个支持的架构下载对应的JDK安装包
2. **放置安装包**：将JDK安装包放置在对应的架构目录下
   - AMD64版本放在 `{version}/x86_64/` 目录
   - ARM64版本放在 `{version}/aarch64/` 目录
3. **文件命名**：确保JDK安装包文件名与`jdk_versions.json`中定义的版本号一致
4. **构建镜像**：运行`build-jdk.sh`脚本构建JDK基础镜像

### 安装包放置示例

```bash
# 为JDK 8放置不同架构的安装包
cp jdk-8u172-linux-x64.tar.gz docker/infrastructure/jdk/shared/jdk/8/x86_64/jdk1.8.0_172.tar.gz
cp jdk-8u172-linux-aarch64.tar.gz docker/infrastructure/jdk/shared/jdk/8/aarch64/jdk1.8.0_172.tar.gz
```

## 当前支持的版本

- **JDK 8**: jdk1.8.0_172.tar.gz
  - AMD64: `8/x86_64/jdk1.8.0_172.tar.gz`
  - ARM64: `8/aarch64/jdk1.8.0_172.tar.gz`

## 镜像标签格式

构建的Docker镜像标签格式如下：
```
<registry>/<namespace>/<repository>:<jdk-major>u<update-number>[-b<build-number>]-jdk-<variant>[-<suffix>]
```

### 标签示例

**基础标签**：
```
192.168.200.39:1443/btit/infra/jdk:8u172-jdk-jammy
```

**带构建号的标签**：
```
192.168.200.39:1443/btit/infra/jdk:8u172-b01-jdk-jammy
```

**带OS后缀的标签**：
```
192.168.200.39:1443/btit/infra/jdk:8u172-b01-jdk-jammy-crypto-sc34
```

### 多架构支持

构建系统会为每个支持的架构生成对应的镜像：
- 同一个标签会根据构建时的 `--platform` 参数选择对应架构的JDK安装包
- 支持跨架构构建（在AMD64系统上构建ARM64镜像，反之亦然）

## 验证安装包

在构建前，可以使用以下命令验证安装包是否正确放置：

```bash
# 检查目录结构
find docker/infrastructure/jdk/shared/jdk -name "*.tar.gz" -type f

# 验证架构支持
./docker/scripts/build/test-arch-support.sh
```

## 注意事项

- **架构匹配**：确保为每个支持的架构都提供了对应的JDK安装包
- **文件完整性**：确保JDK安装包是官方原版，未经修改
- **格式要求**：安装包必须是`.tar.gz`格式
- **许可协议**：请遵循Oracle JDK的许可协议
- **版本一致性**：所有架构的JDK安装包版本必须完全一致
- **文件命名**：文件名必须与配置文件中的版本号严格匹配

## 故障排除

### 常见问题

1. **构建失败：JDK安装包不存在**
   ```
   错误: JDK安装包不存在: .../shared/jdk/8/x86_64/jdk1.8.0_172.tar.gz
   ```
   **解决方案**：检查文件是否放置在正确的架构目录中

2. **架构不匹配**
   ```
   警告: 当前系统架构与目标架构不匹配
   ```
   **解决方案**：现在支持跨架构构建，此警告可以忽略

3. **版本号不匹配**
   ```
   错误: 找不到JDK安装包
   ```
   **解决方案**：检查文件名是否与`jdk_versions.json`中的版本号一致