2025-08-01 18:36:26 [INFO] ===== 测试下载优化功能 =====
2025-08-01 18:36:26 [INFO] 创建了模拟SHA512文件: /mnt/d/work-code/gitlab/shipit/test_resources/apache-tomcat-9.0.100.tar.gz.sha512
2025-08-01 18:36:26 [INFO] SHA512文件内容: d6d6d6d6d6d6d6d6d6d6d6d6d6d6d6d6d6d6d6d6d6d6d6d6d6d6d6d6d6d6d6d6d6d6d6d6d6d6d6d6d6d6d6d6d6d6d6d6d6d6d6d6d6d6d6d6d6d6d6d6d6d6d6d6
2025-08-01 18:36:26 [INFO] 测试download_from_url函数...
2025-08-01 18:36:26 [INFO] 调用download_from_url函数...
2025-08-01 18:36:26 [INFO] - URL: https://example.com/nonexistent/apache-tomcat-9.0.100.tar.gz
2025-08-01 18:36:26 [INFO] - 输出文件: /mnt/d/work-code/gitlab/shipit/test_resources/apache-tomcat-9.0.100.tar.gz
2025-08-01 18:36:26 [INFO] - 验证校验和: true
2025-08-01 18:36:26 [INFO] 从URL下载文件...
2025-08-01 18:36:26 [INFO] - 下载地址: https://example.com/nonexistent/apache-tomcat-9.0.100.tar.gz
2025-08-01 18:36:26 [INFO] - 输出文件: /mnt/d/work-code/gitlab/shipit/test_resources/apache-tomcat-9.0.100.tar.gz
2025-08-01 18:36:26 [INFO] - 是否验证校验和: true
2025-08-01 18:36:26 [INFO] 确保输出目录存在: /mnt/d/work-code/gitlab/shipit/test_resources
2025-08-01 18:36:26 [DEBUG] 资源目录已存在: /mnt/d/work-code/gitlab/shipit/test_resources
2025-08-01 18:36:26 [INFO] 发现本地SHA512文件，使用已存在的文件: /mnt/d/work-code/gitlab/shipit/test_resources/apache-tomcat-9.0.100.tar.gz.sha512
2025-08-01 18:36:26 [INFO] ✓ 成功从本地文件获取SHA512校验和
2025-08-01 18:36:26 [INFO] 检查本地文件: /mnt/d/work-code/gitlab/shipit/test_resources/apache-tomcat-9.0.100.tar.gz
2025-08-01 18:36:26 [DEBUG] 检查本地文件: /mnt/d/work-code/gitlab/shipit/test_resources/apache-tomcat-9.0.100.tar.gz
2025-08-01 18:36:26 [DEBUG] 本地文件不存在: /mnt/d/work-code/gitlab/shipit/test_resources/apache-tomcat-9.0.100.tar.gz
2025-08-01 18:36:26 [INFO] 开始下载文件...
2025-08-01 18:36:26 [INFO] 开始下载 (第 1 次尝试)...
2025-08-01 18:37:00 [WARN] 第 1 次尝试失败，HTTP状态码: 404
2025-08-01 18:37:00 [DEBUG] 下载URL: https://example.com/nonexistent/apache-tomcat-9.0.100.tar.gz
2025-08-01 18:37:00 [INFO] 等待 5 秒后重试...
2025-08-01 18:37:05 [INFO] 开始下载 (第 2 次尝试)...
